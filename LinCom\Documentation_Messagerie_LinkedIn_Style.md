# Documentation - Messagerie Style LinkedIn

## Vue d'ensemble
La page de messagerie a été complètement redesignée pour adopter l'apparence et l'expérience utilisateur de LinkedIn Messaging.

## Modifications apportées

### 1. Structure HTML
- **Container principal** : `.linkedin-messaging-container` - Container responsive avec padding adaptatif
- **Wrapper de messagerie** : `.messaging-wrapper` - Layout flex avec ombres et bordures arrondies
- **Sidebar des conversations** : `.conversations-sidebar` - Panneau latéral fixe de 320px
- **Zone de chat principale** : `.chat-main-panel` - Zone flexible pour les messages

### 2. Sidebar des conversations
- **Header** : Titre "Messagerie" + bouton composer (icône edit)
- **Recherche** : Input avec icône de recherche intégrée
- **Liste des conversations** : 
  - Avatar avec indicateur en ligne
  - Nom du contact
  - Aperçu du dernier message
  - Timestamp
  - Badge de messages non lus
  - État actif/hover

### 3. Zone de chat principale
- **Header du chat** :
  - Avatar du contact avec indicateur de statut
  - Nom et statut en ligne
  - Boutons d'actions (vidéo, audio, options)
- **Zone des messages** : Arrière-plan gris clair avec bulles de messages
- **Zone de saisie** : Input moderne avec toolbar et bouton d'envoi

### 4. Design et couleurs LinkedIn
- **Couleur principale** : #0a66c2 (bleu LinkedIn)
- **Couleur de fond** : #f3f2ef (gris clair LinkedIn)
- **Couleur de succès** : #57c93a (vert en ligne)
- **Typographie** : System fonts (-apple-system, BlinkMacSystemFont, Segoe UI, etc.)

### 5. Fonctionnalités ajoutées
- **Auto-resize** du textarea de saisie
- **Indicateurs de statut** en ligne/hors ligne
- **Animations** d'apparition des messages
- **Scrollbar personnalisée** pour un look moderne
- **Design responsive** pour mobile et tablette

### 6. Responsive Design
- **Desktop** (>1024px) : Layout complet 3 colonnes
- **Tablette** (768-1024px) : Sidebar réduite
- **Mobile** (<768px) : Layout vertical avec sidebar collapsible
- **Petit mobile** (<480px) : Interface optimisée pour petits écrans

## Fichiers modifiés
1. `messagerie.aspx` - Page principale avec nouveau HTML et CSS
2. `images/default-avatar.svg` - Avatar par défaut créé

## Dépendances ajoutées
- **Font Awesome 6.0.0** - Pour les icônes modernes
- **Meta viewport** - Pour le responsive design

## Classes CSS principales

### Layout
- `.linkedin-messaging-container` - Container principal
- `.messaging-wrapper` - Wrapper flex
- `.conversations-sidebar` - Sidebar des conversations
- `.chat-main-panel` - Zone de chat principale

### Conversations
- `.conversation-item` - Item de conversation
- `.conversation-avatar` - Avatar avec indicateur
- `.conversation-content` - Contenu de la conversation
- `.online-indicator` - Indicateur en ligne
- `.unread-badge` - Badge de messages non lus

### Chat
- `.chat-header-linkedin` - Header du chat
- `.chat-messages-area` - Zone des messages
- `.message-container` - Container de message
- `.message-bubble` - Bulle de message

### Input
- `.chat-input-section` - Section de saisie
- `.message-composer` - Compositeur de message
- `.message-input-linkedin` - Input de saisie
- `.send-btn-linkedin` - Bouton d'envoi

## JavaScript amélioré
- Auto-resize du textarea
- Gestion des conversations actives
- Scroll automatique vers le bas
- Gestion améliorée des émojis et pièces jointes

## Notes techniques
- Compatible avec ASP.NET WebForms
- Maintient la fonctionnalité existante
- Améliore l'UX sans casser le backend
- Code CSS organisé et commenté
- Animations performantes avec CSS3
