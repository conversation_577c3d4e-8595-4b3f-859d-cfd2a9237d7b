<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Messagerie Dynamique</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f3f2ef;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            color: #0a66c2;
        }
        
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e6e6e6;
            border-radius: 6px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        
        .test-icon {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .success { color: #57c93a; }
        .warning { color: #f39c12; }
        .error { color: #e74c3c; }
        
        .btn {
            background: #0a66c2;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: background 0.2s;
        }
        
        .btn:hover {
            background: #004182;
            text-decoration: none;
            color: white;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-comments"></i> Test Messagerie Dynamique</h1>
            <p>Vérification des fonctionnalités dynamiques de la messagerie</p>
        </div>

        <div class="test-section">
            <div class="test-title">✅ Fonctionnalités implémentées</div>
            
            <div class="test-item">
                <i class="fas fa-check success test-icon"></i>
                <span><strong>Conversations dynamiques</strong> - Chargement depuis la base de données</span>
            </div>
            
            <div class="test-item">
                <i class="fas fa-check success test-icon"></i>
                <span><strong>Derniers messages</strong> - Affichage du contenu réel</span>
            </div>
            
            <div class="test-item">
                <i class="fas fa-check success test-icon"></i>
                <span><strong>Timestamps dynamiques</strong> - Format "2min", "3h", "2j"</span>
            </div>
            
            <div class="test-item">
                <i class="fas fa-check success test-icon"></i>
                <span><strong>Compteurs non lus</strong> - Badges dynamiques par conversation</span>
            </div>
            
            <div class="test-item">
                <i class="fas fa-check success test-icon"></i>
                <span><strong>Statut en ligne</strong> - Indicateurs verts/gris dynamiques</span>
            </div>
            
            <div class="test-item">
                <i class="fas fa-check success test-icon"></i>
                <span><strong>Actualisation auto</strong> - Conversations toutes les 2min</span>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 Méthodes principales ajoutées</div>
            
            <div class="code-block">
<strong>ChargerConversationsAvecDerniersMessages()</strong>
- Charge les vraies conversations depuis la DB
- Récupère les derniers messages et timestamps
- Calcule les messages non lus par conversation

<strong>Méthodes helper :</strong>
- FormatTempsEcoule() - "2min", "3h", "2j"
- TronquerMessage() - Aperçu des messages
- EstEnLigne() - Statut utilisateur
- GetStatutClass() - Classes CSS dynamiques
- AfficherBadgeNonLus() - Logique d'affichage
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📊 Données utilisées</div>
            
            <div class="test-item">
                <i class="fas fa-database test-icon" style="color: #0a66c2;"></i>
                <span><strong>Tables :</strong> Conversations, Messages, MessageStatus, ParticipantConversations, Membres</span>
            </div>
            
            <div class="test-item">
                <i class="fas fa-link test-icon" style="color: #0a66c2;"></i>
                <span><strong>Jointures :</strong> LINQ avec optimisation des requêtes</span>
            </div>
            
            <div class="test-item">
                <i class="fas fa-shield-alt test-icon" style="color: #0a66c2;"></i>
                <span><strong>Sécurité :</strong> Validation des IDs utilisateur et gestion d'erreurs</span>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🚀 Actions de test</div>
            
            <a href="messagerie.aspx" class="btn">
                <i class="fas fa-comments"></i> Tester la messagerie
            </a>
            
            <a href="messagerie.aspx?debug=1" class="btn">
                <i class="fas fa-bug"></i> Mode debug
            </a>
            
            <button class="btn" onclick="testAjax()">
                <i class="fas fa-sync"></i> Test AJAX
            </button>
        </div>

        <div class="test-section">
            <div class="test-title">📋 Points à vérifier</div>
            
            <div class="test-item">
                <i class="fas fa-eye test-icon warning"></i>
                <span>Les conversations réelles s'affichent-elles ?</span>
            </div>
            
            <div class="test-item">
                <i class="fas fa-clock test-icon warning"></i>
                <span>Les timestamps sont-ils corrects ?</span>
            </div>
            
            <div class="test-item">
                <i class="fas fa-bell test-icon warning"></i>
                <span>Les badges de messages non lus fonctionnent-ils ?</span>
            </div>
            
            <div class="test-item">
                <i class="fas fa-circle test-icon warning"></i>
                <span>Les indicateurs de statut sont-ils dynamiques ?</span>
            </div>
        </div>
    </div>

    <script>
        function testAjax() {
            alert('Test AJAX : Cette fonctionnalité teste les appels AJAX pour marquer les messages comme lus.');
            
            // Simulation d'un appel AJAX
            console.log('Test des méthodes WebMethod...');
            
            // Dans une vraie application, on ferait :
            // fetch('/messagerie.aspx/MarquerConversationLue', { ... })
        }
        
        // Vérifier si la page est accessible
        window.onload = function() {
            console.log('Page de test chargée avec succès');
            console.log('Fonctionnalités dynamiques implémentées ✅');
        };
    </script>
</body>
</html>
