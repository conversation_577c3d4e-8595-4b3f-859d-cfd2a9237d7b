<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Meta viewport pour responsive -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <main class="main">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
<asp:HiddenField ID="hdnIsGroup" runat="server" />
<asp:HiddenField ID="hdnCurrentUserId" runat="server" />

        <div class="linkedin-messaging-container">
            <!-- Header principal de la messagerie -->
            <div class="messaging-header">
                <div class="messaging-nav">
                    <h1 class="messaging-main-title">
                        <i class="fas fa-comments"></i>
                        Messages
                    </h1>
                    <div class="messaging-tabs">
                        <button class="tab-btn active" data-tab="messages">
                            <span>Messages</span>
                            <span class="tab-count">12</span>
                        </button>
                        <button class="tab-btn" data-tab="conversations">
                            <span>Conversations</span>
                        </button>
                    </div>
                    <button type="button" class="compose-btn-header" title="Nouveau message">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div class="messaging-search-global">
                    <div class="search-input-wrapper-global">
                        <i class="fas fa-search search-icon-global"></i>
                        <input type="text" placeholder="Rechercher dans les messages" class="search-input-global">
                        <button type="button" class="search-filter-btn">
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="messaging-wrapper">
                <!-- Sidebar des conversations -->
                <div class="conversations-sidebar">
                    <div class="sidebar-header">
                        <h2 class="messaging-title">Conversations</h2>
                        <button type="button" class="compose-btn" title="Nouveau message">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <asp:TextBox ID="txtRechercheContact" runat="server" placeholder="Rechercher dans la messagerie" AutoPostBack="true" OnTextChanged="txtRechercheContact_TextChanged" CssClass="search-input-linkedin"></asp:TextBox>
                        </div>
                    </div>
                    <div class="conversations-list">
                        <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                            <EmptyDataTemplate>
                                <div class="empty-conversations">
                                    <i class="fas fa-comments empty-icon"></i>
                                    <p>Aucune conversation</p>
                                </div>
                            </EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="conversation-item">
                                    <div class="conversation-avatar">
                                        <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/", Eval("PhotoProfil"))) %>' alt="Photo de profil" />
                                        <div class="online-indicator"></div>
                                    </div>
                                    <div class="conversation-content">
                                        <div class="conversation-header">
                                            <h4 class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></h4>
                                            <span class="conversation-time">2h</span>
                                        </div>
                                        <div class="conversation-preview">
                                            <p class="last-message">Dernier message...</p>
                                            <div class="unread-badge">2</div>
                                        </div>
                                    </div>
                                </asp:LinkButton>
                                <!-- 🔽 Champ caché pour dire si c’est un groupe -->
 
                            </ItemTemplate>
                        </asp:ListView>
                    </div>
                </div>


                <!-- Zone de chat principale -->
                <div class="chat-main-panel">
                    <div class="chat-header-linkedin">
                        <div class="chat-contact-info">
                            <div class="contact-avatar-header">
                                <img src="../images/default-avatar.svg" alt="Contact" id="headerContactAvatar" />
                                <div class="contact-status-indicator"></div>
                            </div>
                            <div class="contact-details">
                                <h3 class="contact-name-header">
                                    <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez un contact pour discuter"></asp:Label>
                                </h3>
                                <span class="contact-status-text">En ligne</span>
                            </div>
                        </div>
                        <div class="chat-actions">
                            <button type="button" class="action-btn" title="Appel vidéo">
                                <i class="fas fa-video"></i>
                            </button>
                            <button type="button" class="action-btn" title="Appel audio">
                                <i class="fas fa-phone"></i>
                            </button>
                            <button type="button" class="action-btn" title="Plus d'options">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                        </div>
                        <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                    </div>

                    <div class="chat-messages-area">
                      <asp:Repeater ID="rptMessages" runat="server">
    <ItemTemplate>
        <div class='message-container <%# Eval("Expediteur").ToString() == "VotreNomComplet" ? "sent" : "received" %>'>
            <div class="message-header">
                <img class="avatar" src='<%# ResolveUrl("~/file/membr/") + Eval("Photomembre") %>' alt="Photo" />
                <strong><%# Eval("Expediteur") %></strong>
                <span class="date"><%# Eval("DateEnvoi", "{0:dd MMM yyyy HH:mm}") %></span>
            </div>

            <div class="message-body">
                <p><%# Server.HtmlDecode(LinCom.Classe.EmojiManager.ConvertirEmojis(Eval("Contenu").ToString())) %></p>

                <%-- Si le message contient une pièce jointe --%>
                <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                    <div class="message-attachment">
                        <span class="attachment-icon"><%# GetFileIcon(Eval("AttachmentUrl").ToString()) %></span>
                        <div class="attachment-details">
                            <div class="attachment-name"><%# Eval("name") %></div>
                            <div class="attachment-size"><%# GetFileSize(Eval("AttachmentUrl").ToString()) %></div>
                        </div>
                        <a href='<%# Eval("AttachmentUrl") %>' class="attachment-download" target="_blank">Télécharger</a>
                    </div>
                </asp:Panel>

                <%-- Affichage du statut de lecture pour les messages envoyés --%>
                <div class="message-footer">
                    <span class="message-status status-read">✓✓</span>
                </div>
            </div>
        </div>
    </ItemTemplate>
</asp:Repeater>

                    </div>

                    <div class="chat-input-section">
                        <div class="message-composer">
                            <div class="composer-toolbar">
                                <button type="button" id="btnEmoji" class="composer-btn" title="Émojis">
                                    <i class="far fa-smile"></i>
                                </button>
                                <button type="button" id="btnAttachment" class="composer-btn" title="Joindre un fichier">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                                <input type="file" id="fileAttachment" style="display:none;" accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.zip,.rar,.mp3,.mp4,.avi" />
                            </div>
                            <div class="message-input-wrapper">
                                <textarea rows="1" runat="server" id="txtMessage" placeholder="Écrivez un message..." maxlength="1000" class="message-input-linkedin"></textarea>
                                <button type="button" runat="server" id="btnenvoie" onserverclick="btnenvoie_ServerClick" class="send-btn-linkedin">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Sélecteur d'émojis -->
                        <div id="emojiPicker" class="emoji-picker" style="display:none;">
                            <div class="emoji-header">
                                <span class="emoji-tab active" data-category="populaires">😊</span>
                                <span class="emoji-tab" data-category="visages">😀</span>
                                <span class="emoji-tab" data-category="gestes">👍</span>
                                <span class="emoji-tab" data-category="objets">❤️</span>
                                <span class="emoji-tab" data-category="nature">🌳</span>
                            </div>
                            <div class="emoji-content" id="emojiContent">
                                <!-- Émojis populaires par défaut -->
                                <div class="emoji-grid">
                                    <span class="emoji-item" data-emoji="😊">😊</span>
                                    <span class="emoji-item" data-emoji="😂">😂</span>
                                    <span class="emoji-item" data-emoji="❤️">❤️</span>
                                    <span class="emoji-item" data-emoji="👍">👍</span>
                                    <span class="emoji-item" data-emoji="😢">😢</span>
                                    <span class="emoji-item" data-emoji="😉">😉</span>
                                    <span class="emoji-item" data-emoji="🔥">🔥</span>
                                    <span class="emoji-item" data-emoji="🎉">🎉</span>
                                    <span class="emoji-item" data-emoji="😍">😍</span>
                                    <span class="emoji-item" data-emoji="👏">👏</span>
                                </div>
                            </div>
                        </div>

                        <!-- Prévisualisation de la pièce jointe -->
                        <div id="attachmentPreview" class="attachment-preview" style="display:none;">
                            <div class="attachment-info">
                                <span id="attachmentName"></span>
                                <span id="attachmentSize"></span>
                                <button type="button" id="btnRemoveAttachment" class="remove-btn">×</button>
                            </div>
                        </div>
                    </div>
                    <div class="message-counter">
                        <small id="charCount">0/1000 caractères</small>
                    </div>

                    <!-- Champ caché pour la pièce jointe -->
                    <asp:HiddenField ID="hdnAttachmentPath" runat="server" />
                </div>
            </div>
        </div>

    </main>

    <style>
        /* === STYLES LINKEDIN MESSAGING === */

        /* Reset et base */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f3f2ef;
        }

        /* Container principal */
        .linkedin-messaging-container {
            width: 100%;
            margin: 0;
            padding: 0;
            background-color: #f3f2ef;
            min-height: 100vh;
        }

        /* === HEADER PRINCIPAL === */
        .messaging-header {
            background: #ffffff;
            border-bottom: 1px solid #e6e6e6;
            padding: 16px 24px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .messaging-nav {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .messaging-main-title {
            font-size: 24px;
            font-weight: 600;
            color: #000000;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .messaging-main-title i {
            color: #0a66c2;
        }

        .messaging-tabs {
            display: flex;
            gap: 8px;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666666;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .tab-btn:hover {
            background: #f3f2ef;
            color: #000000;
        }

        .tab-btn.active {
            background: #e7f3ff;
            color: #0a66c2;
        }

        .tab-count {
            background: #0a66c2;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 11px;
            font-weight: 600;
            min-width: 18px;
            text-align: center;
        }

        .compose-btn-header {
            background: #0a66c2;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .compose-btn-header:hover {
            background: #004182;
        }

        /* Recherche globale */
        .messaging-search-global {
            max-width: 400px;
        }

        .search-input-wrapper-global {
            position: relative;
            display: flex;
            align-items: center;
            background: #f3f2ef;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
        }

        .search-icon-global {
            position: absolute;
            left: 12px;
            color: #666666;
            font-size: 14px;
            z-index: 1;
        }

        .search-input-global {
            width: 100%;
            padding: 10px 12px 10px 36px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            background: transparent;
            outline: none;
        }

        .search-input-global:focus {
            background: #ffffff;
        }

        .search-filter-btn {
            background: none;
            border: none;
            color: #666666;
            cursor: pointer;
            padding: 8px 12px;
            transition: color 0.2s ease;
        }

        .search-filter-btn:hover {
            color: #0a66c2;
        }

        .messaging-wrapper {
            display: flex;
            height: calc(100vh - 140px); /* Ajustement pour le header */
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 0 0 1px rgba(0,0,0,0.15), 0 2px 3px rgba(0,0,0,0.2);
            overflow: hidden;
            margin: 0 20px;
            max-width: calc(100% - 40px);
        }

        /* === SIDEBAR DES CONVERSATIONS === */
        .conversations-sidebar {
            width: 320px;
            background: #ffffff;
            border-right: 1px solid #e6e6e6;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e6e6e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #ffffff;
        }

        .messaging-title {
            font-size: 20px;
            font-weight: 600;
            color: #000000;
            margin: 0;
        }

        .compose-btn {
            background: #0a66c2;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .compose-btn:hover {
            background: #004182;
        }

        /* Barre de recherche */
        .search-container {
            padding: 12px 20px;
            border-bottom: 1px solid #e6e6e6;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            color: #666666;
            font-size: 14px;
            z-index: 1;
        }

        .search-input-linkedin {
            width: 100%;
            padding: 8px 12px 8px 36px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            font-size: 14px;
            background: #f3f2ef;
            transition: border-color 0.2s ease;
        }

        .search-input-linkedin:focus {
            outline: none;
            border-color: #0a66c2;
            background: #ffffff;
        }

        /* Liste des conversations */
        .conversations-list {
            flex: 1;
            overflow-y: auto;
        }

        .conversation-item {
            display: flex;
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 1px solid #f3f2ef;
            transition: background-color 0.2s ease;
            text-decoration: none;
            color: inherit;
        }

        .conversation-item:hover {
            background-color: #f3f2ef;
            text-decoration: none;
            color: inherit;
        }

        .conversation-item.active {
            background-color: #e7f3ff;
            border-left: 3px solid #0a66c2;
        }

        .conversation-avatar {
            position: relative;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .conversation-avatar img {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
        }

        .online-indicator {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #57c93a;
            border: 2px solid #ffffff;
            border-radius: 50%;
        }

        .conversation-content {
            flex: 1;
            min-width: 0;
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .contact-name {
            font-size: 14px;
            font-weight: 600;
            color: #000000;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-time {
            font-size: 12px;
            color: #666666;
            flex-shrink: 0;
            margin-left: 8px;
        }

        .conversation-preview {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .last-message {
            font-size: 13px;
            color: #666666;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
        }

        .unread-badge {
            background: #0a66c2;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 11px;
            font-weight: 600;
            min-width: 18px;
            text-align: center;
            margin-left: 8px;
        }

        .empty-conversations {
            text-align: center;
            padding: 40px 20px;
            color: #666666;
        }

        .empty-icon {
            font-size: 48px;
            color: #cccccc;
            margin-bottom: 16px;
        }
        /* === ZONE DE CHAT PRINCIPALE === */
        .chat-main-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #ffffff;
        }

        .chat-header-linkedin {
            padding: 16px 20px;
            border-bottom: 1px solid #e6e6e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #ffffff;
        }

        .chat-contact-info {
            display: flex;
            align-items: center;
        }

        .contact-avatar-header {
            position: relative;
            margin-right: 12px;
        }

        .contact-avatar-header img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .contact-status-indicator {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 12px;
            height: 12px;
            background: #57c93a;
            border: 2px solid #ffffff;
            border-radius: 50%;
        }

        .contact-details {
            display: flex;
            flex-direction: column;
        }

        .contact-name-header {
            font-size: 16px;
            font-weight: 600;
            color: #000000;
            margin: 0 0 2px 0;
        }

        .contact-status-text {
            font-size: 12px;
            color: #57c93a;
        }

        .chat-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: none;
            border: none;
            color: #666666;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }

        .action-btn:hover {
            background: #f3f2ef;
            color: #0a66c2;
        }

        /* Zone des messages */
        .chat-messages-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f9f9f9;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message-container {
            display: flex;
            margin-bottom: 16px;
        }

        .message-container.sent {
            justify-content: flex-end;
        }

        .message-container.received {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            position: relative;
        }

        .sent .message-bubble {
            background: #0a66c2;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .received .message-bubble {
            background: #ffffff;
            color: #000000;
            border: 1px solid #e6e6e6;
            border-bottom-left-radius: 4px;
        }

        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-size: 12px;
            color: #666666;
        }

        .message-header .avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .message-body p {
            margin: 0;
            word-wrap: break-word;
        }

        .message-footer {
            margin-top: 4px;
            text-align: right;
        }

        .message-status {
            font-size: 11px;
            color: #666666;
        }

        .sent .message-status {
            color: rgba(255, 255, 255, 0.7);
        }

        /* === ZONE DE SAISIE === */
        .chat-input-section {
            border-top: 1px solid #e6e6e6;
            background: #ffffff;
            padding: 16px 20px;
        }

        .message-composer {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .composer-toolbar {
            display: flex;
            gap: 8px;
        }

        .composer-btn {
            background: none;
            border: none;
            color: #666666;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
            font-size: 16px;
        }

        .composer-btn:hover {
            background: #f3f2ef;
            color: #0a66c2;
        }

        .message-input-wrapper {
            display: flex;
            align-items: flex-end;
            gap: 12px;
            background: #f3f2ef;
            border-radius: 24px;
            padding: 8px 12px;
            border: 1px solid #e6e6e6;
            transition: border-color 0.2s ease;
        }

        .message-input-wrapper:focus-within {
            border-color: #0a66c2;
            background: #ffffff;
        }

        .message-input-linkedin {
            flex: 1;
            border: none;
            background: transparent;
            resize: none;
            outline: none;
            font-size: 14px;
            line-height: 1.4;
            padding: 8px 0;
            min-height: 20px;
            max-height: 120px;
            font-family: inherit;
        }

        .message-input-linkedin::placeholder {
            color: #666666;
        }

        .send-btn-linkedin {
            background: #0a66c2;
            color: white;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            font-size: 14px;
        }

        .send-btn-linkedin:hover {
            background: #004182;
        }

        .send-btn-linkedin:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }

        .message-counter {
            padding: 4px 0;
            text-align: right;
            font-size: 12px;
            color: #666666;
        }

        .typing-indicator {
            font-style: italic;
            color: #666666;
            padding: 8px 0;
            font-size: 13px;
            display: none;
        }

        /* === SÉLECTEUR D'ÉMOJIS === */
        .emoji-picker {
            position: absolute;
            bottom: 100%;
            left: 0;
            width: 320px;
            height: 280px;
            background: #ffffff;
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
        }

        .emoji-header {
            display: flex;
            border-bottom: 1px solid #e6e6e6;
            padding: 12px;
            background: #f3f2ef;
            border-radius: 8px 8px 0 0;
        }

        .emoji-tab {
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 4px;
            margin-right: 4px;
            transition: background 0.2s ease;
            font-size: 16px;
        }

        .emoji-tab:hover, .emoji-tab.active {
            background: #e7f3ff;
            color: #0a66c2;
        }

        .emoji-content {
            padding: 12px;
            height: 200px;
            overflow-y: auto;
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 4px;
        }

        .emoji-item {
            padding: 8px;
            text-align: center;
            cursor: pointer;
            border-radius: 4px;
            font-size: 18px;
            transition: all 0.2s ease;
        }

        .emoji-item:hover {
            background: #f3f2ef;
            transform: scale(1.1);
        }

        /* === PIÈCES JOINTES === */
        .attachment-preview {
            background: #f3f2ef;
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            padding: 12px;
            margin-top: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .attachment-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .remove-btn {
            background: #666666;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 14px;
            line-height: 1;
            transition: background-color 0.2s ease;
        }

        .remove-btn:hover {
            background: #333333;
        }

        /* Pièces jointes dans les messages */
        .message-attachment {
            background: #f3f2ef;
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .attachment-icon {
            font-size: 24px;
            color: #0a66c2;
        }

        .attachment-details {
            flex: 1;
        }

        .attachment-name {
            font-weight: 600;
            color: #000000;
            font-size: 14px;
        }

        .attachment-size {
            font-size: 12px;
            color: #666666;
            margin-top: 2px;
        }

        .attachment-download {
            background: #0a66c2;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            text-decoration: none;
            font-size: 12px;
            font-weight: 600;
            transition: background-color 0.2s ease;
        }

        .attachment-download:hover {
            background: #004182;
            color: white;
            text-decoration: none;
        }

        /* Images dans les messages */
        .message-image {
            max-width: 240px;
            max-height: 240px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .message-image:hover {
            transform: scale(1.02);
        }

        /* Statuts de messages */
        .status-sent { color: #666666; }
        .status-delivered { color: #0a66c2; }
        .status-read { color: #57c93a; }

        /* === RESPONSIVE === */
        @media (max-width: 1024px) {
            .conversations-sidebar {
                width: 280px;
            }

            .messaging-header {
                padding: 12px 16px;
            }

            .messaging-nav {
                flex-direction: column;
                gap: 12px;
                align-items: flex-start;
            }

            .messaging-tabs {
                order: -1;
            }

            .messaging-wrapper {
                margin: 0 15px;
                max-width: calc(100% - 30px);
            }
        }

        @media (max-width: 768px) {
            .messaging-header {
                padding: 8px 12px;
            }

            .messaging-nav {
                flex-direction: row;
                justify-content: space-between;
            }

            .messaging-main-title {
                font-size: 20px;
            }

            .messaging-tabs {
                order: 0;
            }

            .messaging-search-global {
                max-width: 100%;
                margin-top: 8px;
            }

            .messaging-wrapper {
                flex-direction: column;
                height: calc(100vh - 160px);
                margin: 0 5px;
                max-width: calc(100% - 10px);
            }

            .conversations-sidebar {
                width: 100%;
                height: 200px;
                border-right: none;
                border-bottom: 1px solid #e6e6e6;
            }

            .chat-main-panel {
                flex: 1;
            }

            .emoji-picker {
                width: 280px;
                height: 200px;
            }

            .emoji-grid {
                grid-template-columns: repeat(6, 1fr);
            }

            .message-image {
                max-width: 180px;
                max-height: 180px;
            }

            .message-bubble {
                max-width: 85%;
            }

            .contact-name-header {
                font-size: 14px;
            }

            .chat-actions {
                gap: 4px;
            }

            .action-btn {
                padding: 6px;
            }
        }

        @media (max-width: 480px) {
            .linkedin-messaging-container {
                padding: 5px;
            }

            .messaging-wrapper {
                border-radius: 0;
                height: 95vh;
            }

            .conversations-sidebar {
                height: 150px;
            }

            .sidebar-header {
                padding: 12px 16px;
            }

            .messaging-title {
                font-size: 18px;
            }

            .compose-btn {
                width: 36px;
                height: 36px;
            }

            .search-container {
                padding: 8px 16px;
            }

            .conversation-item {
                padding: 8px 16px;
            }

            .chat-header-linkedin {
                padding: 12px 16px;
            }

            .chat-messages-area {
                padding: 16px;
            }

            .chat-input-section {
                padding: 12px 16px;
            }
        }

        /* === ANIMATIONS === */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message-container {
            animation: fadeIn 0.3s ease-out;
        }

        .conversation-item {
            transition: all 0.2s ease;
        }

        .conversation-item:active {
            transform: scale(0.98);
        }

        /* === SCROLLBAR PERSONNALISÉE === */
        .conversations-list::-webkit-scrollbar,
        .chat-messages-area::-webkit-scrollbar,
        .emoji-content::-webkit-scrollbar {
            width: 6px;
        }

        .conversations-list::-webkit-scrollbar-track,
        .chat-messages-area::-webkit-scrollbar-track,
        .emoji-content::-webkit-scrollbar-track {
            background: #f3f2ef;
        }

        .conversations-list::-webkit-scrollbar-thumb,
        .chat-messages-area::-webkit-scrollbar-thumb,
        .emoji-content::-webkit-scrollbar-thumb {
            background: #cccccc;
            border-radius: 3px;
        }

        .conversations-list::-webkit-scrollbar-thumb:hover,
        .chat-messages-area::-webkit-scrollbar-thumb:hover,
        .emoji-content::-webkit-scrollbar-thumb:hover {
            background: #999999;
        }
    </style>

    <script type="text/javascript">
        // Compteur de caractères
        function updateCharCount() {
            var textarea = document.getElementById('<%= txtMessage.ClientID %>');
            var counter = document.getElementById('charCount');
            if (textarea && counter) {
                var length = textarea.value.length;
                counter.textContent = length + '/1000 caractères';

                if (length > 900) {
                    counter.style.color = 'red';
                } else if (length > 800) {
                    counter.style.color = 'orange';
                } else {
                    counter.style.color = '#666';
                }
            }
        }

        // Auto-scroll vers le bas des messages
        function scrollToBottom() {
            var chatBody = document.querySelector('.chat-messages-area');
            if (chatBody) {
                chatBody.scrollTop = chatBody.scrollHeight;
            }
        }

        // Auto-resize du textarea
        function autoResizeTextarea() {
            var textarea = document.getElementById('<%= txtMessage.ClientID %>');
            if (textarea) {
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
            }
        }

        // Envoyer message avec Enter (Shift+Enter pour nouvelle ligne)
        function handleKeyPress(event) {
            if (event.keyCode === 13 && !event.shiftKey) {
                event.preventDefault();
                var btnEnvoie = document.getElementById('<%= btnenvoie.ClientID %>');
                if (btnEnvoie) {
                    btnEnvoie.click();
                }
            }
        }

        // Gestion des émojis
        function toggleEmojiPicker() {
            var picker = document.getElementById('emojiPicker');
            picker.style.display = picker.style.display === 'none' ? 'block' : 'none';
        }

        function insertEmoji(emoji) {
            var textarea = document.getElementById('<%= txtMessage.ClientID %>');
            var cursorPos = textarea.selectionStart;
            var textBefore = textarea.value.substring(0, cursorPos);
            var textAfter = textarea.value.substring(cursorPos);

            textarea.value = textBefore + emoji + textAfter;
            textarea.focus();
            textarea.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);

            updateCharCount();
            document.getElementById('emojiPicker').style.display = 'none';
        }

        // Gestion des pièces jointes
        function handleFileSelect() {
            var fileInput = document.getElementById('fileAttachment');
            var file = fileInput.files[0];

            if (file) {
                // Validation de la taille (10MB max)
                if (file.size > 10 * 1024 * 1024) {
                    alert('Le fichier est trop volumineux (maximum 10 Mo)');
                    fileInput.value = '';
                    return;
                }

                // Validation de l'extension
                var allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.txt', '.zip', '.rar', '.mp3', '.mp4', '.avi'];
                var fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

                if (allowedExtensions.indexOf(fileExtension) === -1) {
                    alert('Type de fichier non autorisé. Extensions autorisées: ' + allowedExtensions.join(', '));
                    fileInput.value = '';
                    return;
                }

                // Afficher la prévisualisation
                showAttachmentPreview(file);

                // Uploader le fichier immédiatement
                uploadFile(file);
            }
        }

        // Fonction pour uploader le fichier (version simplifiée)
        function uploadFile(file) {
            // Pour l'instant, on stocke juste les informations du fichier
            // L'upload réel se fera lors de l'envoi du message

            var nameSpan = document.getElementById('attachmentName');
            var sizeSpan = document.getElementById('attachmentSize');

            // Afficher les informations du fichier
            nameSpan.textContent = file.name;
            sizeSpan.textContent = formatFileSize(file.size);

            // Marquer qu'un fichier est prêt à être uploadé
            document.getElementById('<%= hdnAttachmentPath.ClientID %>').value = 'READY_TO_UPLOAD';

            console.log('Fichier prêt pour upload: ' + file.name);
        }

        function showAttachmentPreview(file) {
            var preview = document.getElementById('attachmentPreview');
            var nameSpan = document.getElementById('attachmentName');
            var sizeSpan = document.getElementById('attachmentSize');

            nameSpan.textContent = file.name;
            sizeSpan.textContent = formatFileSize(file.size);
            preview.style.display = 'block';
        }

        function removeAttachment() {
            document.getElementById('fileAttachment').value = '';
            document.getElementById('attachmentPreview').style.display = 'none';
            document.getElementById('<%= hdnAttachmentPath.ClientID %>').value = '';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // Initialisation au chargement de la page
        window.onload = function() {
            var textarea = document.getElementById('<%= txtMessage.ClientID %>');
            if (textarea) {
                textarea.addEventListener('input', function() {
                    updateCharCount();
                    autoResizeTextarea();
                });
                textarea.addEventListener('keypress', handleKeyPress);
            }

            // Gestion du bouton émoji
            document.getElementById('btnEmoji').addEventListener('click', toggleEmojiPicker);

            // Gestion du bouton pièce jointe
            document.getElementById('btnAttachment').addEventListener('click', function() {
                document.getElementById('fileAttachment').click();
            });

            // Gestion de la sélection de fichier
            document.getElementById('fileAttachment').addEventListener('change', handleFileSelect);

            // Gestion de la suppression de pièce jointe
            document.getElementById('btnRemoveAttachment').addEventListener('click', removeAttachment);

            // Gestion des clics sur les émojis
            document.querySelectorAll('.emoji-item').forEach(function(item) {
                item.addEventListener('click', function() {
                    insertEmoji(this.dataset.emoji);
                });
            });

            // Fermer le sélecteur d'émojis en cliquant ailleurs
            document.addEventListener('click', function(e) {
                var picker = document.getElementById('emojiPicker');
                var btnEmoji = document.getElementById('btnEmoji');

                if (!picker.contains(e.target) && e.target !== btnEmoji) {
                    picker.style.display = 'none';
                }
            });

            scrollToBottom();
            updateCharCount();

            // Marquer les conversations comme actives au clic
            document.querySelectorAll('.conversation-item').forEach(function(item) {
                item.addEventListener('click', function() {
                    // Retirer la classe active de tous les éléments
                    document.querySelectorAll('.conversation-item').forEach(function(el) {
                        el.classList.remove('active');
                    });
                    // Ajouter la classe active à l'élément cliqué
                    this.classList.add('active');
                });
            });
        };

        // Actualiser les messages toutes les 30 secondes
        setInterval(function() {
            if (document.getElementById('<%= lblId.ClientID %>').textContent !== '0') {
                __doPostBack('<%= Page.ClientID %>', 'RefreshMessages');
            }
        }, 30000);
    </script>

</asp:Content>
