# Corrections pour la visibilité du header du site

## Problème identifié
La page de messagerie masquait complètement le header principal du site "Community Burundi" à cause d'un design plein écran.

## Solutions appliquées

### 1. Ajustement du container principal
```css
.main {
    background-color: #f3f2ef;
    min-height: calc(100vh - 80px); /* Espace pour le header du site */
    padding: 0;
    margin-top: 0; /* S'assurer qu'il n'y a pas de marge qui cache le header */
}

.linkedin-messaging-container {
    width: 100%;
    margin: 0;
    padding: 20px 0 0 0; /* Espace en haut pour séparer du header du site */
    background-color: #f3f2ef;
    min-height: calc(100vh - 100px); /* Ajustement pour le header du site */
}
```

### 2. Modification du header de messagerie
- Changé de `position: sticky` à `position: relative`
- Réduit le z-index de 100 à 10
- Permet au header du site de rester visible

### 3. Ajustement des hauteurs
```css
.messaging-wrapper {
    height: calc(100vh - 220px); /* Ajustement pour le header du site + header messagerie */
}
```

### 4. Responsive design amélioré
```css
@media (max-width: 768px) {
    .messaging-wrapper {
        height: calc(100vh - 200px); /* Ajustement pour mobile avec header du site */
    }
}
```

## Structure finale
1. **Header du site** (Community Burundi) - Toujours visible en haut
2. **Header de messagerie** - Avec titre "Messages" et onglets
3. **Interface de messagerie** - Style LinkedIn avec sidebar et chat

## Fichiers modifiés
- `messagerie.aspx` - Page principale avec corrections CSS
- `test-messagerie.html` - Page de test pour vérifier la visibilité

## Test
Ouvrir `test-messagerie.html` pour voir la simulation du header du site et tester le lien vers la messagerie.

## Résultat attendu
- Header "Community Burundi" visible en permanence
- Interface de messagerie style LinkedIn fonctionnelle
- Design responsive qui s'adapte aux différentes tailles d'écran
- Aucun élément masqué ou coupé
