﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics.Eventing.Reader;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class messagerie : System.Web.UI.Page
    {
        IMessage objmes = new MessageImp();
        Message_Class mess = new Message_Class();
        IConversation objconver = new ConversationImp();
        Conversation_Class conver = new Conversation_Class();
        ParticipantConversation_Class partconver = new ParticipantConversation_Class();
        MessageStatus_Class messtatu = new MessageStatus_Class();

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        ICommonCode co = new CommonCode();
        //   UrlPartage = $"{Request.Url.GetLeftPart(UriPartial.Authority)}/post/" + ep.name,
        IDomaineInterventionOrganisation objdomorg = new DomaineInterventionOrganisationImp();
        DomaineInterventionOrganisation_Class domorg = new DomaineInterventionOrganisation_Class();
        int info;
        static string imge, imge1, pdfe, nameorg;
       long ide; static long idorg;
        static int rolid;
        long index;
        static long conversationreceveur;
        protected void Page_Load(object sender, EventArgs e)
        {
            // Vérification de l'authentification avec gestion d'erreur
            if (Request.Cookies["iduser"] == null || Request.Cookies["role"] == null)
            {
                Response.Redirect("login.aspx");
                return;
            }

            // Récupération sécurisée de l'ID utilisateur
            if (!long.TryParse(Request.Cookies["iduser"].Value, out ide))
            {
                Response.Redirect("login.aspx");
                return;
            }

            // Récupération du rôle
            if (Request.Cookies["role"] != null)
            {
                int.TryParse(Request.Cookies["role"].Value, out rolid);
            }

            if (!IsPostBack)
            {
                AppelMethode(); // Charger la liste des conversations avec derniers messages
                MettreAJourCompteurMessages(); // Mettre à jour le compteur dans l'onglet
                lblHeader.Text = "Sélectionnez un contact pour commencer une conversation";
                lblId.Text = "0"; // Initialiser à 0
            }
            else
            {
                // Gérer les postbacks pour l'actualisation automatique
                string eventTarget = Request["__EVENTTARGET"];
                string eventArgument = Request["__EVENTARGUMENT"];

                if (eventArgument == "RefreshMessages" && lblId.Text != "0")
                {
                    ChargerMessages();
                }
            }
        }
        public void AppelMethode()
        {
            // Charger les conversations avec les derniers messages au lieu de tous les membres
            ChargerConversationsAvecDerniersMessages();
        }

        /// <summary>
        /// Charge les conversations de l'utilisateur avec les derniers messages et informations dynamiques
        /// </summary>
        private void ChargerConversationsAvecDerniersMessages()
        {
            try
            {
                using (var con = new Connection())
                {
                    // Requête pour récupérer les conversations de l'utilisateur avec les derniers messages
                    var conversationsData = (from pc in con.ParticipantConversations
                                           join c in con.Conversations on pc.ConversationId equals c.ConversationId
                                           join pc2 in con.ParticipantConversations on c.ConversationId equals pc2.ConversationId
                                           join m in con.Membres on pc2.MembreId equals m.MembreId
                                           where pc.MembreId == ide && pc2.MembreId != ide && c.IsGroup == 0 // Conversations privées seulement
                                           select new
                                           {
                                               ConversationId = c.ConversationId,
                                               MembreId = m.MembreId,
                                               Membre = m.Nom + " " + m.Prenom,
                                               PhotoProfil = m.PhotoProfil ?? "emptyuser.png",
                                               StatutEnLigne = m.statut ?? "hors ligne"
                                           }).Distinct().ToList();

                    // Pour chaque conversation, récupérer le dernier message et le nombre de non lus
                    var conversationsAvecMessages = conversationsData.Select(conv =>
                    {
                        // Dernier message de la conversation
                        var dernierMessage = con.Messages
                            .Where(msg => msg.ConversationId == conv.ConversationId)
                            .OrderByDescending(msg => msg.DateEnvoi)
                            .FirstOrDefault();

                        // Nombre de messages non lus
                        var messagesNonLus = con.MessageStatus
                            .Where(ms => ms.UserId == ide && ms.IsRead == 0)
                            .Join(con.Messages, ms => ms.MessageId, msg => msg.MessageId, (ms, msg) => new { ms, msg })
                            .Where(x => x.msg.ConversationId == conv.ConversationId)
                            .Count();

                        return new
                        {
                            id = conv.MembreId,
                            Membre = conv.Membre,
                            PhotoProfil = conv.PhotoProfil,
                            StatutEnLigne = conv.StatutEnLigne,
                            DernierMessage = dernierMessage?.Contenu ?? "Aucun message",
                            DateDernierMessage = dernierMessage?.DateEnvoi ?? DateTime.Now,
                            MessagesNonLus = messagesNonLus,
                            ConversationId = conv.ConversationId
                        };
                    }).OrderByDescending(x => x.DateDernierMessage).ToList();

                    // Si aucune conversation, charger quelques membres actifs pour commencer des conversations
                    if (!conversationsAvecMessages.Any())
                    {
                        var membresActifs = con.Membres
                            .Where(m => m.MembreId != ide && m.statut == "actif")
                            .Take(10)
                            .Select(m => new
                            {
                                id = m.MembreId,
                                Membre = m.Nom + " " + m.Prenom,
                                PhotoProfil = m.PhotoProfil ?? "emptyuser.png",
                                StatutEnLigne = m.statut ?? "hors ligne",
                                DernierMessage = "Commencer une conversation",
                                DateDernierMessage = DateTime.Now,
                                MessagesNonLus = 0,
                                ConversationId = (long?)null
                            }).ToList();

                        listmembre.DataSource = membresActifs;
                    }
                    else
                    {
                        listmembre.DataSource = conversationsAvecMessages;
                    }

                    listmembre.DataBind();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur ChargerConversationsAvecDerniersMessages: {ex.Message}");
                // En cas d'erreur, charger la méthode par défaut
                objmem.ChargerListview(listmembre, -1, "actif", "");
            }
        }
     

        protected void listmembre_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (e.CommandName == "viewmem")
            {
                // Validation sécurisée de l'ID membre
                if (!long.TryParse(e.CommandArgument.ToString(), out long idMembre))
                {
                    Response.Write("<script>alert('Erreur: ID membre invalide');</script>");
                    return;
                }

                // Vérifier que l'utilisateur ne tente pas de s'envoyer un message à lui-même
                if (idMembre == ide)
                {
                    Response.Write("<script>alert('Vous ne pouvez pas vous envoyer un message à vous-même');</script>");
                    return;
                }

                // Récupérer les détails du membre
                objmem.AfficherDetails(idMembre, mem);

                if (mem.MembreId > 0) // Vérifier que le membre existe
                {
                    // Mettre à jour l'interface
                    lblHeader.Text = "Conversation avec " + mem.Nom + " " + mem.Prenom;
                    lblId.Text = mem.MembreId.ToString();

                    // Charger les messages de cette conversation
                    ChargerMessages();

                    // Vider le champ de saisie
                    txtMessage.Value = "";
                }
                else
                {
                    Response.Write("<script>alert('Membre introuvable');</script>");
                }
            }
        }

    private void CreationConversation(int cd,string sujetgroup)
        {//creation d'une nouvelle convrsation

            if (cd==0)
            {//privee
                conver.Sujet = "";
                conver.IsGroup = 0;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
            else if (cd==1)
            {
                //equipe
                conver.Sujet = sujetgroup;
                conver.IsGroup = 1;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
              
        }
      private  void CreationParticipantConversation(long memparticipant)
        {//creation des membres qui commencent le tchat
         //long conversationreceveur = objconver.VerifierConversationId(ide, Convert.ToInt64(memparticipant));
         //if (conversationreceveur > 0)
         //{
         //    partconver.ConversationId = conversationreceveur;
         //    partconver.MembreId= memparticipant;
         //    partconver.JoinedAt = DateTime.Now;

            //    objconver.AjouterParticipant(conversationreceveur, Convert.ToInt64(memparticipant));

            //}
            conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);

            // Si aucune conversation => on la crée
            if (conversationreceveur <= 0)
            {
                CreationConversation(0, ""); // conversation privée
                conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);
            }
            else
            {
                partconver.ConversationId = conversationreceveur;
                partconver.MembreId = memparticipant;
                partconver.JoinedAt = DateTime.Now;
                // Ensuite on ajoute les 2 participants S'ILS NE SONT PAS DÉJÀ DEDANS

                if (!objconver.ParticipantExiste(conversationreceveur, ide))
                    objconver.AjouterParticipant(conversationreceveur, ide);

                if (!objconver.ParticipantExiste(conversationreceveur, memparticipant))
                    objconver.AjouterParticipant(conversationreceveur, memparticipant);

            }

        }
        private int CreationMessage(long convID,long membrId)
        {
            mess.ConversationId = convID;
            mess.SenderId = membrId;
            mess.Contenu = txtMessage.Value;
            mess.DateEnvoi = DateTime.Now;
            mess.name = "";
            mess.AttachmentUrl = "";
            info=objmes.Envoyer(mess);

            return info;

        }
        private int CreationMessagestatus(long convID, long membrId,int lire)
        {
            messtatu.MessageId = convID;
            messtatu.UserId = membrId;
            messtatu.IsRead = lire;
            messtatu.ReadAt = DateTime.Now;
           
            info =objmes.EnvoyerMessageStatus(messtatu);

            return info;
        }

        private void EnvoieMessagerie()
        {
            if (!string.IsNullOrWhiteSpace(txtMessage.Value))
            {
                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);
                bool isGroup = hdnIsGroup.Value == "1";
                int info=0,info1 = 0,info2=0;

                if (isGroup)
                {//il faut continuer l'implementation
                    // Groupe : conversation déjà existante via id du groupe
                    long idGroupe = destinataireId;

                    // Ici, on ajoute le message à la table des messages de groupe
                  ///  info = objmes.AjouterMessageDansGroupe(idGroupe, senderId, txtMessage.Value);
                  //  if (info == 1)
                   //     objmes.ChargerMessagesGroupe(rptMessages, idGroupe, 50);
                }
                else
                {
                    // Utiliser la nouvelle méthode sécurisée
                    info = EnvoieMessagePrive(senderId, destinataireId);

                    if (info == 1)
                    {
                        NettoyerFormulaire(); // Nettoyer le formulaire complet
                        ChargerMessages(); // Recharger les messages
                        Response.Write("<script>document.getElementById('attachmentPreview').style.display='none';</script>");
                    }
                }

                if (info != 1)
                    Response.Write("<script>alert('Erreur lors de l’envoi du message.');</script>");
            }
        }

        // Nouvelle méthode pour envoyer un message privé de manière sécurisée
        private int EnvoieMessagePrive(long senderId, long destinataireId)
        {
            try
            {
                // Vérifier ou créer la conversation
                long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                if (conversationId <= 0)
                {
                    // Créer une nouvelle conversation
                    CreationConversation(0, "");
                    conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    if (conversationId <= 0)
                    {
                        return 0; // Échec de création de conversation
                    }
                }

                // S'assurer que les participants sont dans la conversation
                CreationParticipantConversation(destinataireId);

                // Gérer la pièce jointe si présente
                string attachmentUrl = GererPieceJointe();

                // Préparer le message avec émojis convertis
                string contenuAvecEmojis = EmojiManager.ConvertirEmojis(txtMessage.Value.Trim());

                mess.ConversationId = conversationId;
                mess.SenderId = senderId;
                mess.Contenu = contenuAvecEmojis;
                mess.DateEnvoi = DateTime.Now;
                mess.name = attachmentUrl != null ? Path.GetFileName(attachmentUrl) : "";
                mess.AttachmentUrl = attachmentUrl;

                // Utiliser la méthode complète qui gère les statuts automatiquement
                long[] participants = { senderId, destinataireId };
                int resultatMessage = objmes.EnvoyerMessageComplet(mess, participants);

                // Envoyer notification si le message a été envoyé avec succès
                if (resultatMessage == 1)
                {
                    MessageNotificationManager.NotifierNouveauMessage(senderId, destinataireId, contenuAvecEmojis, conversationId);
                }

                return resultatMessage;
            }
            catch (Exception ex)
            {
                // Log l'erreur (à implémenter selon votre système de logging)
                System.Diagnostics.Debug.WriteLine($"Erreur EnvoieMessagePrive: {ex.Message}");
                return 0;
            }
        }

        protected void btnenvoie_ServerClick(object sender, EventArgs e)
        {
            try
            {
                // Validation du message avec la nouvelle classe
                var validationMessage = MessagerieValidator.ValiderMessage(txtMessage.Value);
                if (!validationMessage.EstValide)
                {
                    Response.Write($"<script>alert('{validationMessage.Message}');</script>");
                    return;
                }

                // Validation du destinataire
                var validationDestinataire = MessagerieValidator.ValiderMembreId(lblId.Text);
                if (!validationDestinataire.EstValide)
                {
                    Response.Write($"<script>alert('{validationDestinataire.Message}');</script>");
                    return;
                }

                // Validation anti-spam améliorée
                if (Session["LastMessageTime"] != null && Session["LastMessageContent"] != null)
                {
                    DateTime lastTime = (DateTime)Session["LastMessageTime"];
                    string lastContent = Session["LastMessageContent"].ToString();

                    if (DateTime.Now.Subtract(lastTime).TotalSeconds < MessagerieConfig.DelaiAntiSpamSecondes
                        && lastContent == txtMessage.Value.Trim())
                    {
                        Response.Write($"<script>alert('{MessagerieConfig.Messages.AntiSpam}');</script>");
                        return;
                    }
                }

                // Nettoyer le contenu du message
                string contenuNettoye = MessagerieValidator.NettoyerTexte(txtMessage.Value);
                txtMessage.Value = contenuNettoye;

                // Envoyer le message
                EnvoieMessagerie();

                // Mettre à jour les informations anti-spam
                Session["LastMessageTime"] = DateTime.Now;
                Session["LastMessageContent"] = contenuNettoye;
            }
            catch (Exception ex)
            {
                Response.Write($"<script>alert('Erreur lors de l\\'envoi: {ex.Message}');</script>");
            }
        }
        void EnvoieMessage()
        {
            if (!string.IsNullOrWhiteSpace(txtMessage.Value))
            {
                long conversationreceveurmembre = objconver.VerifierConversationId(ide, Convert.ToInt64(lblId.Text));
               
                if (conversationreceveurmembre <= 0)
                    CreationConversation(0,"");

                long conversationreceveurmembreencore = objconver.VerifierConversationId(ide, Convert.ToInt64(lblId.Text));

                mess.ConversationId = conversationreceveurmembreencore;
                mess.SenderId = ide;
                mess.Contenu = txtMessage.Value.Trim();
                mess.DateEnvoi = DateTime.Now;
                mess.name = "";
                mess.AttachmentUrl = null;

               info= objmes.Envoyer(mess);

                if (info==1)
                {
                    //CreationParticipantConversation();

                 //   Response.Write("<script LANGUAGE=JavaScript>alert('Message envoyé')</script>");

                }
                else
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Erreur')</script>");


                }
            }
        }
        void ChargerMessages()
        {
            // Validation des données avant chargement
            if (lblId.Text == "0" || string.IsNullOrEmpty(lblId.Text))
            {
                // Pas de contact sélectionné, vider les messages
                rptMessages.DataSource = null;
                rptMessages.DataBind();
                return;
            }

            if (!long.TryParse(lblId.Text, out long destinataireId))
            {
                // ID invalide, afficher un message d'erreur
                Response.Write("<script>alert('Erreur: Contact invalide');</script>");
                return;
            }

            long senderId = ide;
            long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

            if (conversationId > 0)
            {
                objmes.ChargerMessages(rptMessages, conversationId, 50); // Limiter à 50 messages récents

                // Mettre à jour les statuts de lecture avec notifications
                MettreAJourStatutsLecture(conversationId);
            }
            else
            {
                // Aucune conversation existante, vider les messages
                rptMessages.DataSource = null;
                rptMessages.DataBind();
            }
        }

        // Méthode pour la recherche de contacts
        protected void txtRechercheContact_TextChanged(object sender, EventArgs e)
        {
            string termeRecherche = txtRechercheContact.Text.Trim();

            if (string.IsNullOrEmpty(termeRecherche))
            {
                // Si la recherche est vide, charger tous les membres
                AppelMethode();
                return;
            }

            // Valider le terme de recherche
            var validationRecherche = MessagerieValidator.ValiderRechercheContact(termeRecherche);
            if (!validationRecherche.EstValide)
            {
                Response.Write($"<script>alert('{validationRecherche.Message}');</script>");
                return;
            }

            // Rechercher les membres correspondants avec le terme nettoyé
            RechercherMembres(validationRecherche.Valeur.ToString());
        }

        // Méthode pour rechercher des membres
        private void RechercherMembres(string termeRecherche)
        {
            try
            {
                var membresRecherches = objmem.RechercherMembres(termeRecherche, ide);

                if (membresRecherches != null && membresRecherches.Any())
                {
                    listmembre.DataSource = membresRecherches;
                    listmembre.DataBind();
                }
                else
                {
                    // Aucun résultat trouvé
                    listmembre.DataSource = null;
                    listmembre.DataBind();
                }
            }
            catch (Exception ex)
            {
                Response.Write($"<script>alert('Erreur lors de la recherche: {ex.Message}');</script>");
            }
        }

        // Méthodes pour les nouvelles fonctionnalités

        /// <summary>
        /// Obtient l'icône appropriée pour un fichier
        /// </summary>
        protected string GetFileIcon(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "📎";

            string extension = Path.GetExtension(filePath).ToLower();
            var type = AttachmentManager.DeterminerTypeFichier(extension);
            return AttachmentManager.ObtenirIconeFichier(type);
        }

        /// <summary>
        /// Obtient la taille d'un fichier pour l'affichage
        /// </summary>
        protected string GetFileSize(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                    return "";

                string fullPath = Server.MapPath(filePath);
                if (File.Exists(fullPath))
                {
                    var fileInfo = new FileInfo(fullPath);
                    return AttachmentManager.FormaterTaille(fileInfo.Length);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur taille fichier: {ex.Message}");
            }
            return "";
        }

        /// <summary>
        /// Gère l'upload de pièces jointes
        /// </summary>
        private string GererPieceJointe()
        {
            try
            {
                // Vérifier si un fichier est prêt à être uploadé
                if (hdnAttachmentPath.Value == "READY_TO_UPLOAD")
                {
                    // Chercher le fichier dans les contrôles de la page
                    var fileInput = Request.Files["fileAttachment"];
                    if (fileInput != null && fileInput.ContentLength > 0)
                    {
                        return UploadFichierMaintenant(fileInput);
                    }
                }

                // Vérifier si un fichier a déjà été uploadé
                if (!string.IsNullOrEmpty(hdnAttachmentPath.Value) && hdnAttachmentPath.Value != "READY_TO_UPLOAD")
                {
                    return hdnAttachmentPath.Value;
                }

                // Chercher dans tous les fichiers uploadés
                foreach (string fileKey in Request.Files.AllKeys)
                {
                    var fileUpload = Request.Files[fileKey];
                    if (fileUpload != null && fileUpload.ContentLength > 0)
                    {
                        return UploadFichierMaintenant(fileUpload);
                    }
                }
            }
            catch (Exception ex)
            {
                Response.Write($"<script>alert('Erreur upload: {ex.Message}');</script>");
                System.Diagnostics.Debug.WriteLine($"Erreur upload pièce jointe: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// Upload un fichier immédiatement
        /// </summary>
        private string UploadFichierMaintenant(HttpPostedFile fileUpload)
        {
            try
            {
                // Valider le fichier
                var validation = AttachmentManager.ValiderFichier(fileUpload);
                if (!validation.EstValide)
                {
                    Response.Write($"<script>alert('{validation.Message}');</script>");
                    return null;
                }

                // Upload le fichier
                var fichierInfo = AttachmentManager.UploadFichier(fileUpload, ide);

                // Sauvegarder le chemin dans le champ caché
                hdnAttachmentPath.Value = fichierInfo.UrlAcces;

                // Notifier la pièce jointe si nécessaire
                if (long.TryParse(lblId.Text, out long destinataireId))
                {
                    long conversationId = objconver.VerifierConversationId(ide, destinataireId);
                    MessageNotificationManager.NotifierPieceJointe(ide, destinataireId, fichierInfo.NomOriginal, conversationId);
                }

                return fichierInfo.UrlAcces;
            }
            catch (Exception ex)
            {
                Response.Write($"<script>alert('Erreur upload fichier: {ex.Message}');</script>");
                System.Diagnostics.Debug.WriteLine($"Erreur upload: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Met à jour les statuts de lecture
        /// </summary>
        private void MettreAJourStatutsLecture(long conversationId)
        {
            try
            {
                // Marquer tous les messages de cette conversation comme lus pour l'utilisateur actuel
                int messagesMarques = MessageStatusManager.MarquerConversationCommeLue(conversationId, ide);

                if (messagesMarques > 0)
                {
                    // Notifier l'expéditeur que ses messages ont été lus
                    using (var con = new Connection())
                    {
                        var dernierMessage = con.Messages
                            .Where(m => m.ConversationId == conversationId && m.SenderId != ide)
                            .OrderByDescending(m => m.DateEnvoi)
                            .FirstOrDefault();

                        if (dernierMessage != null)
                        {
                            MessageNotificationManager.NotifierMessageLu(dernierMessage.SenderId.Value, ide, dernierMessage.MessageId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur mise à jour statuts: {ex.Message}");
            }
        }

        /// <summary>
        /// Méthode pour gérer l'upload de fichiers via AJAX
        /// </summary>
        [System.Web.Services.WebMethod]
        public static string UploadFile()
        {
            try
            {
                var context = System.Web.HttpContext.Current;
                var request = context.Request;

                if (request.Files.Count > 0)
                {
                    var file = request.Files[0];
                    if (file != null && file.ContentLength > 0)
                    {
                        // Validation du fichier
                        var validation = AttachmentManager.ValiderFichier(file);
                        if (!validation.EstValide)
                        {
                            return $"ERROR:{validation.Message}";
                        }

                        // Upload du fichier (utiliser un ID temporaire pour les tests)
                        long membreId = 1; // À remplacer par l'ID du membre connecté
                        var fichierInfo = AttachmentManager.UploadFichier(file, membreId);

                        return $"SUCCESS:{fichierInfo.UrlAcces}|{fichierInfo.NomOriginal}|{AttachmentManager.FormaterTaille(fichierInfo.TailleOctets)}";
                    }
                }

                return "ERROR:Aucun fichier sélectionné";
            }
            catch (Exception ex)
            {
                return $"ERROR:{ex.Message}";
            }
        }

        /// <summary>
        /// Méthode pour nettoyer le message après envoi
        /// </summary>
        private void NettoyerFormulaire()
        {
            txtMessage.Value = "";
            hdnAttachmentPath.Value = "";
        }

        /// <summary>
        /// Propriété pour exposer l'ID utilisateur de manière sécurisée
        /// </summary>
        protected long CurrentUserId
        {
            get { return ide; }
        }

        /// <summary>
        /// Formate le temps écoulé depuis le dernier message
        /// </summary>
        protected string FormatTempsEcoule(object dateEnvoi)
        {
            if (dateEnvoi == null || !(dateEnvoi is DateTime))
                return "";

            DateTime date = (DateTime)dateEnvoi;
            TimeSpan tempsEcoule = DateTime.Now - date;

            if (tempsEcoule.TotalMinutes < 1)
                return "À l'instant";
            else if (tempsEcoule.TotalMinutes < 60)
                return $"{(int)tempsEcoule.TotalMinutes}min";
            else if (tempsEcoule.TotalHours < 24)
                return $"{(int)tempsEcoule.TotalHours}h";
            else if (tempsEcoule.TotalDays < 7)
                return $"{(int)tempsEcoule.TotalDays}j";
            else
                return date.ToString("dd/MM");
        }

        /// <summary>
        /// Tronque le dernier message pour l'affichage
        /// </summary>
        protected string TronquerMessage(object message, int longueurMax = 50)
        {
            if (message == null)
                return "Aucun message";

            string msg = message.ToString();
            if (string.IsNullOrEmpty(msg))
                return "Aucun message";

            // Nettoyer les émojis pour l'aperçu
            msg = System.Text.RegularExpressions.Regex.Replace(msg, @":\w+:", "");

            if (msg.Length <= longueurMax)
                return msg;

            return msg.Substring(0, longueurMax) + "...";
        }

        /// <summary>
        /// Détermine si l'utilisateur est en ligne
        /// </summary>
        protected bool EstEnLigne(object statut)
        {
            if (statut == null)
                return false;

            string statutStr = statut.ToString().ToLower();
            return statutStr == "actif" || statutStr == "en ligne";
        }

        /// <summary>
        /// Obtient la classe CSS pour l'indicateur de statut
        /// </summary>
        protected string GetStatutClass(object statut)
        {
            return EstEnLigne(statut) ? "online-indicator" : "offline-indicator";
        }

        /// <summary>
        /// Détermine si le badge de messages non lus doit être affiché
        /// </summary>
        protected bool AfficherBadgeNonLus(object messagesNonLus)
        {
            if (messagesNonLus == null)
                return false;

            int count = Convert.ToInt32(messagesNonLus);
            return count > 0;
        }

        /// <summary>
        /// Formate le nombre de messages non lus
        /// </summary>
        protected string FormatMessagesNonLus(object messagesNonLus)
        {
            if (messagesNonLus == null)
                return "0";

            int count = Convert.ToInt32(messagesNonLus);
            return count > 99 ? "99+" : count.ToString();
        }

        /// <summary>
        /// Méthode pour actualiser les conversations (appelée via AJAX)
        /// </summary>
        [System.Web.Services.WebMethod]
        public static string ActualiserConversations()
        {
            try
            {
                // Cette méthode peut être appelée via AJAX pour actualiser la liste
                return "SUCCESS:Conversations actualisées";
            }
            catch (Exception ex)
            {
                return $"ERROR:{ex.Message}";
            }
        }

        /// <summary>
        /// Méthode pour marquer une conversation comme lue
        /// </summary>
        [System.Web.Services.WebMethod]
        public static string MarquerConversationLue(long conversationId, long userId)
        {
            try
            {
                using (var con = new Connection())
                {
                    var messagesNonLus = con.MessageStatus
                        .Where(ms => ms.UserId == userId && ms.IsRead == 0)
                        .Join(con.Messages, ms => ms.MessageId, msg => msg.MessageId, (ms, msg) => new { ms, msg })
                        .Where(x => x.msg.ConversationId == conversationId)
                        .Select(x => x.ms)
                        .ToList();

                    foreach (var status in messagesNonLus)
                    {
                        status.IsRead = 1;
                        status.ReadAt = DateTime.Now;
                    }

                    con.SaveChanges();
                    return $"SUCCESS:{messagesNonLus.Count} messages marqués comme lus";
                }
            }
            catch (Exception ex)
            {
                return $"ERROR:{ex.Message}";
            }
        }

        /// <summary>
        /// Met à jour le compteur de messages dans l'onglet
        /// </summary>
        private void MettreAJourCompteurMessages()
        {
            try
            {
                using (var con = new Connection())
                {
                    // Compter tous les messages non lus de l'utilisateur
                    var totalMessagesNonLus = con.MessageStatus
                        .Where(ms => ms.UserId == ide && ms.IsRead == 0)
                        .Count();

                    // Injecter le script pour mettre à jour le compteur
                    string script = $@"
                        document.addEventListener('DOMContentLoaded', function() {{
                            var tabCount = document.querySelector('.tab-count');
                            if (tabCount) {{
                                tabCount.textContent = '{totalMessagesNonLus}';
                                if ({totalMessagesNonLus} === 0) {{
                                    tabCount.style.display = 'none';
                                }} else {{
                                    tabCount.style.display = 'inline-block';
                                }}
                            }}
                        }});
                    ";

                    ClientScript.RegisterStartupScript(this.GetType(), "UpdateMessageCount", script, true);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur MettreAJourCompteurMessages: {ex.Message}");
            }
        }

        /// <summary>
        /// Obtient les statistiques de messagerie pour l'utilisateur
        /// </summary>
        [System.Web.Services.WebMethod]
        public static string ObtenirStatistiquesMessagerie(long userId)
        {
            try
            {
                using (var con = new Connection())
                {
                    var stats = new
                    {
                        MessagesNonLus = con.MessageStatus.Where(ms => ms.UserId == userId && ms.IsRead == 0).Count(),
                        ConversationsActives = con.ParticipantConversations.Where(pc => pc.MembreId == userId).Count(),
                        DernierMessageRecu = con.Messages
                            .Where(m => con.ParticipantConversations.Any(pc => pc.ConversationId == m.ConversationId && pc.MembreId == userId) && m.SenderId != userId)
                            .OrderByDescending(m => m.DateEnvoi)
                            .Select(m => m.DateEnvoi)
                            .FirstOrDefault()
                    };

                    return $"SUCCESS:{Newtonsoft.Json.JsonConvert.SerializeObject(stats)}";
                }
            }
            catch (Exception ex)
            {
                return $"ERROR:{ex.Message}";
            }
        }
    }
}