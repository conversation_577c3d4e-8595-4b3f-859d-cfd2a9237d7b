# Documentation - Messagerie Dynamique

## Vue d'ensemble
La page de messagerie a été rendue complètement dynamique avec des données provenant de la base de données en temps réel.

## Fonctionnalités dynamiques implémentées

### 1. Liste des conversations dynamique
- **Chargement des vraies conversations** depuis la base de données
- **Derniers messages** affichés avec contenu réel
- **Timestamps dynamiques** (format : "2min", "3h", "2j", etc.)
- **Compteur de messages non lus** par conversation
- **Statut en ligne/hors ligne** des contacts

### 2. Méthodes principales ajoutées

#### `ChargerConversationsAvecDerniersMessages()`
```csharp
// Charge les conversations avec les derniers messages et informations dynamiques
// Récupère : ConversationId, MembreId, Nom, Photo, Statut, DernierMessage, DateDernierMessage, MessagesNonLus
```

#### Méthodes helper pour l'affichage
- `FormatTempsEcoule()` - Formate le temps écoulé depuis le dernier message
- `TronquerMessage()` - Tronque les messages longs pour l'aperçu
- `EstEnLigne()` - Détermine si l'utilisateur est en ligne
- `GetStatutClass()` - Retourne la classe CSS pour l'indicateur de statut
- `AfficherBadgeNonLus()` - Détermine si le badge doit être affiché
- `FormatMessagesNonLus()` - Formate le nombre de messages non lus

### 3. Template HTML dynamique

#### Avant (statique)
```html
<span class="conversation-time">2h</span>
<p class="last-message">Dernier message...</p>
<div class="unread-badge">2</div>
```

#### Après (dynamique)
```html
<span class="conversation-time"><%# FormatTempsEcoule(Eval("DateDernierMessage")) %></span>
<p class="last-message"><%# TronquerMessage(Eval("DernierMessage")) %></p>
<div class="unread-badge" style='<%# AfficherBadgeNonLus(Eval("MessagesNonLus")) ? "display: block" : "display: none" %>'>
    <%# FormatMessagesNonLus(Eval("MessagesNonLus")) %>
</div>
```

### 4. Indicateurs de statut dynamiques
- **Vert** : Utilisateur en ligne (`online-indicator`)
- **Gris** : Utilisateur hors ligne (`offline-indicator`)
- **Basé sur** : Champ `statut` de la table `Membres`

### 5. Actualisation automatique
- **Conversations** : Actualisées toutes les 2 minutes
- **Messages** : Actualisés toutes les 30 secondes
- **Marquage automatique** des messages comme lus

### 6. Méthodes WebMethod pour AJAX

#### `MarquerConversationLue()`
```csharp
[WebMethod]
public static string MarquerConversationLue(long conversationId, long userId)
// Marque tous les messages d'une conversation comme lus
```

#### `ObtenirStatistiquesMessagerie()`
```csharp
[WebMethod] 
public static string ObtenirStatistiquesMessagerie(long userId)
// Retourne les statistiques de messagerie (messages non lus, conversations actives, etc.)
```

### 7. Compteur dynamique dans l'onglet
- **Mise à jour automatique** du nombre total de messages non lus
- **Masquage automatique** si aucun message non lu
- **Script JavaScript** injecté pour la mise à jour

## Structure de données utilisée

### Tables principales
- `Conversations` - Conversations entre utilisateurs
- `Messages` - Messages individuels
- `MessageStatus` - Statut de lecture des messages
- `ParticipantConversations` - Participants aux conversations
- `Membres` - Informations des utilisateurs

### Requête principale
```sql
SELECT DISTINCT 
    c.ConversationId,
    m.MembreId,
    m.Nom + ' ' + m.Prenom as Membre,
    m.PhotoProfil,
    m.statut as StatutEnLigne
FROM ParticipantConversations pc
JOIN Conversations c ON pc.ConversationId = c.ConversationId
JOIN ParticipantConversations pc2 ON c.ConversationId = pc2.ConversationId
JOIN Membres m ON pc2.MembreId = m.MembreId
WHERE pc.MembreId = @userId 
  AND pc2.MembreId != @userId 
  AND c.IsGroup = 0
```

## Gestion des erreurs
- **Try-catch** sur toutes les méthodes de base de données
- **Fallback** vers la méthode statique en cas d'erreur
- **Logging** des erreurs dans Debug.WriteLine

## Améliorations apportées
1. **Performance** : Requêtes optimisées avec LINQ
2. **UX** : Données en temps réel
3. **Sécurité** : Validation des IDs utilisateur
4. **Responsive** : Fonctionne sur tous les appareils
5. **Accessibilité** : Images avec fallback et alt text

## Tests recommandés
1. Vérifier l'affichage des conversations réelles
2. Tester le formatage des timestamps
3. Valider les compteurs de messages non lus
4. Vérifier les indicateurs de statut
5. Tester l'actualisation automatique

## Prochaines étapes possibles
- Notifications push en temps réel
- Recherche dans les conversations
- Archivage des conversations
- Groupes de discussion
- Partage de fichiers amélioré
